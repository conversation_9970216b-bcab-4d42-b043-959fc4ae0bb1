.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  white-space: nowrap;
  min-height: 44px; /* Touch-friendly minimum */
}

.btn:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Sizes */
.btn--sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn--md {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
  min-height: 44px;
}

.btn--lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* Variants */
.btn--primary {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-primary);
}

.btn--primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn--primary:active:not(:disabled) {
  transform: translateY(1px);
}

.btn--secondary {
  background-color: var(--color-gray-100);
  color: var(--color-text-primary);
  border-color: var(--color-gray-200);
}

.btn--secondary:hover:not(:disabled) {
  background-color: var(--color-gray-200);
  border-color: var(--color-gray-300);
}

.btn--outline {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn--outline:hover:not(:disabled) {
  background-color: var(--color-primary-light);
  color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn--ghost {
  background-color: transparent;
  color: var(--color-primary);
  border-color: transparent;
}

.btn--ghost:hover:not(:disabled) {
  background-color: var(--color-primary-light);
  color: var(--color-primary-hover);
}

/* Full width */
.btn--full-width {
  width: 100%;
}

/* Loading state */
.btn--loading {
  pointer-events: none;
}

.btn__spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.btn__spinner-icon {
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

.btn__spinner-circle {
  stroke: currentColor;
  stroke-linecap: round;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
  animation: dash 2s ease-in-out infinite;
}

.btn__content--loading {
  opacity: 0;
}

.btn__content {
  opacity: 1;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .btn--sm {
    min-height: 40px;
    padding: var(--spacing-2) var(--spacing-3);
  }
  
  .btn--md {
    min-height: 48px;
    padding: var(--spacing-3) var(--spacing-5);
  }
  
  .btn--lg {
    min-height: 56px;
    padding: var(--spacing-4) var(--spacing-6);
  }
}
