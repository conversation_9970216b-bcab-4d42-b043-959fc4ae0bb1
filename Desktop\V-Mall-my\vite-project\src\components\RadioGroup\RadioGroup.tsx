import { forwardRef } from 'react';
import './RadioGroup.css';

export interface RadioOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface RadioGroupProps {
  name: string;
  options: RadioOption[];
  value?: string;
  onChange?: (value: string) => void;
  label?: string;
  error?: string;
  helperText?: string;
  direction?: 'horizontal' | 'vertical';
  className?: string;
}

export const RadioGroup = forwardRef<HTMLFieldSetElement, RadioGroupProps>(({
  name,
  options,
  value,
  onChange,
  label,
  error,
  helperText,
  direction = 'vertical',
  className = '',
  ...props
}, ref) => {
  const hasError = Boolean(error);
  const groupId = `radio-group-${Math.random().toString(36).substr(2, 9)}`;
  
  const groupClasses = [
    'radio-group',
    `radio-group--${direction}`,
    hasError ? 'radio-group--error' : '',
    className
  ].filter(Boolean).join(' ');

  const handleChange = (optionValue: string) => {
    if (onChange) {
      onChange(optionValue);
    }
  };

  return (
    <fieldset
      ref={ref}
      className={groupClasses}
      aria-invalid={hasError}
      aria-describedby={
        error ? `${groupId}-error` : 
        helperText ? `${groupId}-helper` : 
        undefined
      }
      {...props}
    >
      {label && (
        <legend className="radio-group-label">
          {label}
        </legend>
      )}
      
      <div className="radio-group-options">
        {options.map((option) => {
          const radioId = `${name}-${option.value}`;
          const isChecked = value === option.value;
          
          return (
            <div key={option.value} className="radio-option">
              <input
                type="radio"
                id={radioId}
                name={name}
                value={option.value}
                checked={isChecked}
                disabled={option.disabled}
                onChange={() => handleChange(option.value)}
                className="radio-input"
              />
              
              <div className="radio-indicator" aria-hidden="true">
                <div className="radio-dot" />
              </div>
              
              <label htmlFor={radioId} className="radio-label">
                {option.label}
              </label>
            </div>
          );
        })}
      </div>
      
      {error && (
        <div id={`${groupId}-error`} className="radio-group-error" role="alert">
          {error}
        </div>
      )}
      
      {helperText && !error && (
        <div id={`${groupId}-helper`} className="radio-group-helper">
          {helperText}
        </div>
      )}
    </fieldset>
  );
});

RadioGroup.displayName = 'RadioGroup';

export default RadioGroup;
