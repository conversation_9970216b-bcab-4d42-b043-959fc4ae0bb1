.input-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.input-field--full-width {
  width: 100%;
}

.input-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  min-height: 44px; /* Touch-friendly minimum */
}

.input::placeholder {
  color: var(--color-text-muted);
}

.input:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input:disabled {
  background-color: var(--color-gray-50);
  color: var(--color-text-muted);
  cursor: not-allowed;
}

.input--error {
  border-color: var(--color-border-error);
}

.input--error:focus {
  border-color: var(--color-border-error);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.input--with-left-icon {
  padding-left: var(--spacing-12);
}

.input--with-right-icon {
  padding-right: var(--spacing-12);
}

.input--full-width {
  width: 100%;
}

.input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: var(--color-text-muted);
  pointer-events: none;
}

.input-icon--left {
  left: var(--spacing-4);
}

.input-icon--right {
  right: var(--spacing-4);
}

.input-icon svg {
  width: 100%;
  height: 100%;
}

.input-error {
  font-size: var(--font-size-sm);
  color: var(--color-error);
  line-height: var(--line-height-tight);
}

.input-helper {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  line-height: var(--line-height-tight);
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .input {
    min-height: 48px;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
  }
  
  .input--with-left-icon {
    padding-left: var(--spacing-10);
  }
  
  .input--with-right-icon {
    padding-right: var(--spacing-10);
  }
  
  .input-icon--left {
    left: var(--spacing-3);
  }
  
  .input-icon--right {
    right: var(--spacing-3);
  }
}

/* Focus states for better accessibility */
@media (prefers-reduced-motion: no-preference) {
  .input {
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  }
}

@media (prefers-reduced-motion: reduce) {
  .input {
    transition: none;
  }
}
