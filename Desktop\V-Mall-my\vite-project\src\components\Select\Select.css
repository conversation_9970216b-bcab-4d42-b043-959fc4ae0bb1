.select-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.select-field--full-width {
  width: 100%;
}

.select-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
}

.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.select {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-12) var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  min-height: 44px; /* Touch-friendly minimum */
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.select:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.select:disabled {
  background-color: var(--color-gray-50);
  color: var(--color-text-muted);
  cursor: not-allowed;
}

.select--error {
  border-color: var(--color-border-error);
}

.select--error:focus {
  border-color: var(--color-border-error);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.select--full-width {
  width: 100%;
}

.select-icon {
  position: absolute;
  right: var(--spacing-4);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--color-text-muted);
  pointer-events: none;
  transition: transform var(--transition-fast);
}

.select:focus + .select-icon {
  transform: translateY(-50%) rotate(180deg);
}

.select:disabled + .select-icon {
  color: var(--color-gray-400);
}

.select-icon svg {
  width: 100%;
  height: 100%;
}

.select-error {
  font-size: var(--font-size-sm);
  color: var(--color-error);
  line-height: var(--line-height-tight);
}

.select-helper {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  line-height: var(--line-height-tight);
}

/* Option styling */
.select option {
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
}

.select option:disabled {
  color: var(--color-text-muted);
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .select {
    min-height: 48px;
    padding: var(--spacing-3) var(--spacing-10) var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
  }
  
  .select-icon {
    right: var(--spacing-3);
    width: 18px;
    height: 18px;
  }
}

/* Focus states for better accessibility */
@media (prefers-reduced-motion: no-preference) {
  .select {
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  }
  
  .select-icon {
    transition: transform var(--transition-fast);
  }
}

@media (prefers-reduced-motion: reduce) {
  .select,
  .select-icon {
    transition: none;
  }
}
