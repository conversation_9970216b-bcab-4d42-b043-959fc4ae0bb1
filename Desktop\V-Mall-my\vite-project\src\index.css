@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    @apply box-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply font-sans antialiased bg-dark-950 text-white min-h-screen;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-950 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-white text-dark-950 hover:bg-gray-100 focus:ring-white;
  }

  .btn-secondary {
    @apply bg-dark-800 text-white border border-dark-700 hover:bg-dark-700 focus:ring-dark-600;
  }

  .btn-outline {
    @apply border border-white text-white hover:bg-white hover:text-dark-950 focus:ring-white;
  }

  .btn-google {
    @apply bg-white text-dark-950 hover:bg-gray-100 focus:ring-white;
  }

  .btn-facebook {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .input-field {
    @apply w-full px-4 py-3 bg-dark-900 border border-dark-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent transition-all duration-200;
  }

  .input-error {
    @apply border-red-500 focus:ring-red-500;
  }

  .avatar-boy {
    @apply bg-gradient-to-br from-blue-500 to-blue-600;
  }

  .avatar-girl {
    @apply bg-gradient-to-br from-pink-500 to-pink-600;
  }

  .card {
    @apply bg-dark-900 border border-dark-800 rounded-xl p-6 shadow-xl;
  }

  .glass-effect {
    @apply bg-dark-900/80 backdrop-blur-sm border border-dark-800/50;
  }
}


