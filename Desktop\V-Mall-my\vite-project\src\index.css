@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #020617;
    color: white;
    min-height: 100vh;
    margin: 0;
    padding: 0;
  }
}

@layer components {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    border: none;
    cursor: pointer;
    min-height: 44px;
  }

  .btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: white;
    color: #020617;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #f3f4f6;
  }

  .btn-secondary {
    background-color: #1e293b;
    color: white;
    border: 1px solid #334155;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: #334155;
  }

  .btn-outline {
    border: 1px solid white;
    color: white;
    background-color: transparent;
  }

  .btn-outline:hover:not(:disabled) {
    background-color: white;
    color: #020617;
  }

  .btn-google {
    background-color: white;
    color: #020617;
  }

  .btn-google:hover:not(:disabled) {
    background-color: #f3f4f6;
  }

  .btn-facebook {
    background-color: #2563eb;
    color: white;
  }

  .btn-facebook:hover:not(:disabled) {
    background-color: #1d4ed8;
  }

  .input-field {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: #0f172a;
    border: 1px solid #334155;
    border-radius: 0.5rem;
    color: white;
    font-size: 1rem;
    transition: all 0.2s ease-in-out;
    min-height: 44px;
  }

  .input-field::placeholder {
    color: #9ca3af;
  }

  .input-field:focus {
    outline: none;
    border-color: white;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  }

  .input-error {
    border-color: #ef4444;
  }

  .input-error:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
  }

  .card {
    background-color: #0f172a;
    border: 1px solid #1e293b;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  }

  .glass-effect {
    background-color: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(30, 41, 59, 0.5);
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}
