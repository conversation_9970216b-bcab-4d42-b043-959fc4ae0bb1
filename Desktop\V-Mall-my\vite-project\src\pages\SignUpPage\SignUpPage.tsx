import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';

interface SignUpFormData {
  email: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

interface VerificationFormData {
  code: string;
}

export const SignUpPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'signup' | 'verification'>('signup');
  const [email, setEmail] = useState('');
  const navigate = useNavigate();

  const {
    register: registerSignup,
    handleSubmit: handleSubmitSignup,
    formState: { errors: signupErrors },
    watch,
  } = useForm<SignUpFormData>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      agreeToTerms: false
    }
  });

  const {
    register: registerVerification,
    handleSubmit: handleSubmitVerification,
    formState: { errors: verificationErrors },
  } = useForm<VerificationFormData>({
    defaultValues: {
      code: ''
    }
  });

  const password = watch('password');

  const validateEmail = (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!value) return 'البريد الإلكتروني مطلوب';
    if (!emailRegex.test(value)) return 'يرجى إدخال بريد إلكتروني صحيح';
    return true;
  };

  const validatePassword = (value: string) => {
    if (!value) return 'كلمة المرور مطلوبة';
    if (value.length < 8) return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    if (!/(?=.*[a-z])/.test(value)) return 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    if (!/(?=.*[A-Z])/.test(value)) return 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    if (!/(?=.*\d)/.test(value)) return 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    return true;
  };

  const validateConfirmPassword = (value: string) => {
    if (!value) return 'يرجى تأكيد كلمة المرور';
    if (value !== password) return 'كلمات المرور غير متطابقة';
    return true;
  };

  const onSignupSubmit = async (data: SignUpFormData) => {
    setIsLoading(true);

    // Simulate API call to create account and send verification email
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('Sign up data:', data);
    setEmail(data.email);
    setStep('verification');
    setIsLoading(false);
  };

  const onVerificationSubmit = async (data: VerificationFormData) => {
    setIsLoading(true);

    // Simulate API call to verify code
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('Verification code:', data.code);
    setIsLoading(false);

    // Navigate to onboarding
    navigate('/onboarding');
  };

  const resendCode = async () => {
    setIsLoading(true);

    // Simulate API call to resend verification code
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('Resending verification code to:', email);
    setIsLoading(false);
  };

  if (step === 'verification') {
    return (
      <div className="min-h-screen bg-dark-950 flex items-center justify-center p-4">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-950 to-black"></div>
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>

        <div className="relative w-full max-w-md">
          <div className="card glass-effect animate-fade-in">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-white mb-2">تأكيد البريد الإلكتروني</h1>
              <p className="text-gray-400 mb-2">أدخل رمز التأكيد المرسل إلى</p>
              <p className="text-white font-medium">{email}</p>
            </div>

            <form onSubmit={handleSubmitVerification(onVerificationSubmit)} className="space-y-6">
              {/* Verification Code Input */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  رمز التأكيد
                </label>
                <input
                  {...registerVerification('code', {
                    required: 'رمز التأكيد مطلوب',
                    pattern: {
                      value: /^\d{6}$/,
                      message: 'رمز التأكيد يجب أن يكون 6 أرقام'
                    }
                  })}
                  type="text"
                  placeholder="000000"
                  maxLength={6}
                  className={`input-field text-center text-2xl tracking-widest ${verificationErrors.code ? 'input-error' : ''}`}
                />
                {verificationErrors.code && (
                  <p className="mt-1 text-sm text-red-400">{verificationErrors.code.message}</p>
                )}
              </div>

              {/* Verify Button */}
              <button
                type="submit"
                disabled={isLoading}
                className="btn btn-primary w-full py-4 text-lg font-semibold"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-dark-950" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    جاري التحقق...
                  </div>
                ) : (
                  'تأكيد الرمز'
                )}
              </button>
            </form>

            {/* Resend Code */}
            <div className="mt-6 text-center">
              <p className="text-gray-400 mb-2">لم تستلم الرمز؟</p>
              <button
                onClick={resendCode}
                disabled={isLoading}
                className="text-white hover:text-gray-300 font-medium transition-colors"
              >
                إعادة إرسال الرمز
              </button>
            </div>

            {/* Back to signup */}
            <div className="mt-8 text-center">
              <button
                onClick={() => setStep('signup')}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ← العودة للتسجيل
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-950 flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-950 to-black"></div>
      <div className="absolute inset-0 opacity-20" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      <div className="relative w-full max-w-md">
        <div className="card glass-effect animate-fade-in">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-white to-gray-300 rounded-2xl mx-auto mb-4 flex items-center justify-center">
              <svg className="w-8 h-8 text-dark-950" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">إنشاء حساب جديد</h1>
            <p className="text-gray-400">أدخل بياناتك لإنشاء حساب</p>
          </div>

          <form onSubmit={handleSubmitSignup(onSignupSubmit)} className="space-y-6">
            {/* Email Input */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                البريد الإلكتروني
              </label>
              <div className="relative">
                <input
                  {...registerSignup('email', {
                    validate: validateEmail
                  })}
                  type="email"
                  placeholder="أدخل بريدك الإلكتروني"
                  className={`input-field ${signupErrors.email ? 'input-error' : ''}`}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
              </div>
              {signupErrors.email && (
                <p className="mt-1 text-sm text-red-400">{signupErrors.email.message}</p>
              )}
            </div>

            {/* Password Input */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                كلمة المرور
              </label>
              <div className="relative">
                <input
                  {...registerSignup('password', {
                    validate: validatePassword
                  })}
                  type="password"
                  placeholder="أدخل كلمة مرور قوية"
                  className={`input-field ${signupErrors.password ? 'input-error' : ''}`}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              {signupErrors.password && (
                <p className="mt-1 text-sm text-red-400">{signupErrors.password.message}</p>
              )}
            </div>

            {/* Confirm Password Input */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                تأكيد كلمة المرور
              </label>
              <div className="relative">
                <input
                  {...registerSignup('confirmPassword', {
                    validate: validateConfirmPassword
                  })}
                  type="password"
                  placeholder="أعد إدخال كلمة المرور"
                  className={`input-field ${signupErrors.confirmPassword ? 'input-error' : ''}`}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              {signupErrors.confirmPassword && (
                <p className="mt-1 text-sm text-red-400">{signupErrors.confirmPassword.message}</p>
              )}
            </div>

            {/* Terms Agreement */}
            <div className="space-y-3">
              <label className="flex items-start">
                <input
                  {...registerSignup('agreeToTerms', {
                    required: 'يجب الموافقة على الشروط والأحكام'
                  })}
                  type="checkbox"
                  className="h-4 w-4 text-white bg-dark-800 border-dark-600 rounded focus:ring-white focus:ring-2 mt-1"
                />
                <span className="mr-3 text-sm text-gray-300">
                  أوافق على{' '}
                  <a href="/terms" className="text-white hover:text-gray-300 underline">
                    الشروط والأحكام
                  </a>
                  {' '}و{' '}
                  <a href="/privacy" className="text-white hover:text-gray-300 underline">
                    سياسة الخصوصية
                  </a>
                </span>
              </label>
              {signupErrors.agreeToTerms && (
                <p className="text-sm text-red-400">{signupErrors.agreeToTerms.message}</p>
              )}
            </div>

            {/* Sign Up Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="btn btn-primary w-full py-4 text-lg font-semibold"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-dark-950" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  جاري إنشاء الحساب...
                </div>
              ) : (
                'إنشاء حساب'
              )}
            </button>
          </form>

          {/* Login Link */}
          <div className="mt-8 text-center">
            <p className="text-gray-400">
              لديك حساب بالفعل؟{' '}
              <Link to="/login" className="text-white hover:text-gray-300 font-medium transition-colors">
                تسجيل الدخول
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;
