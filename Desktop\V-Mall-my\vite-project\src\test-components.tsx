// Simple test file to validate components are working
import React from 'react';
import { Button, Input, Checkbox, RadioGroup, Select } from './components';

export const TestComponents: React.FC = () => {
  return (
    <div style={{ padding: '2rem', maxWidth: '600px', margin: '0 auto' }}>
      <h1>Component Test Page</h1>
      
      <div style={{ marginBottom: '2rem' }}>
        <h2>Buttons</h2>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="primary" isLoading>Loading</Button>
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Inputs</h2>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <Input label="Email" placeholder="Enter your email" />
          <Input label="Password" type="password" placeholder="Enter password" />
          <Input label="With Error" error="This field is required" />
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Checkbox</h2>
        <Checkbox label="I agree to the terms" />
        <Checkbox label="With error" error="This is required" />
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Radio Group</h2>
        <RadioGroup
          name="test-radio"
          label="Choose an option"
          options={[
            { value: 'option1', label: 'Option 1' },
            { value: 'option2', label: 'Option 2' },
            { value: 'option3', label: 'Option 3' }
          ]}
        />
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Select</h2>
        <Select
          label="Choose a language"
          placeholder="Select language"
          options={[
            { value: 'en', label: 'English' },
            { value: 'es', label: 'Spanish' },
            { value: 'fr', label: 'French' }
          ]}
        />
      </div>
    </div>
  );
};
