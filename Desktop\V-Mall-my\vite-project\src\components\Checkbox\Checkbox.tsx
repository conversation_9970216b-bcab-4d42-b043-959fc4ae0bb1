import React, { forwardRef } from 'react';
import './Checkbox.css';

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
  error?: string;
  helperText?: string;
}

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(({
  label,
  error,
  helperText,
  className = '',
  id,
  ...props
}, ref) => {
  const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = Boolean(error);
  
  const checkboxClasses = [
    'checkbox',
    hasError ? 'checkbox--error' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="checkbox-field">
      <div className="checkbox-wrapper">
        <input
          ref={ref}
          type="checkbox"
          id={checkboxId}
          className={checkboxClasses}
          aria-invalid={hasError}
          aria-describedby={
            error ? `${checkboxId}-error` : 
            helperText ? `${checkboxId}-helper` : 
            undefined
          }
          {...props}
        />
        
        <div className="checkbox-indicator" aria-hidden="true">
          <svg className="checkbox-icon" viewBox="0 0 16 16">
            <path
              d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
              fill="currentColor"
            />
          </svg>
        </div>
        
        {label && (
          <label htmlFor={checkboxId} className="checkbox-label">
            {label}
          </label>
        )}
      </div>
      
      {error && (
        <div id={`${checkboxId}-error`} className="checkbox-error" role="alert">
          {error}
        </div>
      )}
      
      {helperText && !error && (
        <div id={`${checkboxId}-helper`} className="checkbox-helper">
          {helperText}
        </div>
      )}
    </div>
  );
});

Checkbox.displayName = 'Checkbox';

export default Checkbox;
