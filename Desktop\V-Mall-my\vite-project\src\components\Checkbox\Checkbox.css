.checkbox-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.checkbox-wrapper {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  cursor: pointer;
}

.checkbox {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  min-width: 20px;
  min-height: 20px;
  background-color: var(--color-bg-primary);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  margin-top: 2px; /* Align with first line of text */
}

.checkbox-icon {
  width: 12px;
  height: 12px;
  color: var(--color-text-inverse);
  opacity: 0;
  transform: scale(0.8);
  transition: all var(--transition-fast);
}

.checkbox:checked + .checkbox-indicator {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox:checked + .checkbox-indicator .checkbox-icon {
  opacity: 1;
  transform: scale(1);
}

.checkbox:focus + .checkbox-indicator {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.checkbox:disabled + .checkbox-indicator {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-300);
  cursor: not-allowed;
}

.checkbox:disabled:checked + .checkbox-indicator {
  background-color: var(--color-gray-400);
  border-color: var(--color-gray-400);
}

.checkbox--error + .checkbox-indicator {
  border-color: var(--color-border-error);
}

.checkbox--error:focus + .checkbox-indicator {
  outline-color: var(--color-border-error);
}

.checkbox-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
  cursor: pointer;
  user-select: none;
}

.checkbox:disabled ~ .checkbox-label {
  color: var(--color-text-muted);
  cursor: not-allowed;
}

.checkbox-error {
  font-size: var(--font-size-sm);
  color: var(--color-error);
  line-height: var(--line-height-tight);
  margin-left: calc(20px + var(--spacing-3)); /* Align with label */
}

.checkbox-helper {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  line-height: var(--line-height-tight);
  margin-left: calc(20px + var(--spacing-3)); /* Align with label */
}

/* Hover states */
.checkbox-wrapper:hover .checkbox:not(:disabled) + .checkbox-indicator {
  border-color: var(--color-primary);
}

.checkbox-wrapper:hover .checkbox:not(:disabled):checked + .checkbox-indicator {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

/* Touch-friendly sizing for mobile */
@media (max-width: 767px) {
  .checkbox-indicator {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
  }
  
  .checkbox-icon {
    width: 14px;
    height: 14px;
  }
  
  .checkbox-error,
  .checkbox-helper {
    margin-left: calc(24px + var(--spacing-3));
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .checkbox-indicator,
  .checkbox-icon {
    transition: none;
  }
}
