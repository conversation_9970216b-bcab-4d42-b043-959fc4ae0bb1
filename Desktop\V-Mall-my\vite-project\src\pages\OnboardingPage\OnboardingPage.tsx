import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

interface OnboardingFormData {
  language: string;
  gender: 'male' | 'female';
}

const languageOptions = [
  { value: 'ar', label: 'العربية' },
  { value: 'en', label: 'English' },
  { value: 'fr', label: 'Français' },
  { value: 'es', label: 'Español' },
  { value: 'de', label: 'Deutsch' },
  { value: 'it', label: 'Italiano' },
  { value: 'pt', label: 'Português' },
  { value: 'ru', label: 'Русский' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'zh', label: '中文' },
  { value: 'hi', label: 'हिन्दी' }
];

const boyAvatars = [
  { id: 'boy-1', name: 'أحمد', emoji: '👨‍💼' },
  { id: 'boy-2', name: 'محمد', emoji: '👨‍🎓' },
  { id: 'boy-3', name: 'علي', emoji: '👨‍💻' },
  { id: 'boy-4', name: 'عمر', emoji: '👨‍🔬' },
  { id: 'boy-5', name: 'يوسف', emoji: '👨‍🎨' },
  { id: 'boy-6', name: 'كريم', emoji: '👨‍🏫' }
];

const girlAvatars = [
  { id: 'girl-1', name: 'فاطمة', emoji: '👩‍💼' },
  { id: 'girl-2', name: 'عائشة', emoji: '👩‍🎓' },
  { id: 'girl-3', name: 'مريم', emoji: '👩‍💻' },
  { id: 'girl-4', name: 'زينب', emoji: '👩‍🔬' },
  { id: 'girl-5', name: 'سارة', emoji: '👩‍🎨' },
  { id: 'girl-6', name: 'نور', emoji: '👩‍🏫' }
];

export const OnboardingPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState<string>('');
  const [selectedGender, setSelectedGender] = useState<'male' | 'female' | ''>('');
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm<OnboardingFormData>({
    defaultValues: {
      language: 'ar',
      gender: 'male'
    }
  });

  // const watchedLanguage = watch('language'); // Not used currently

  const handleGenderSelect = (gender: 'male' | 'female') => {
    setSelectedGender(gender);
    setValue('gender', gender);
    setSelectedAvatar(''); // Reset avatar when gender changes
  };

  const handleAvatarSelect = (avatarId: string) => {
    setSelectedAvatar(avatarId);
  };

  const onSubmit = async (data: OnboardingFormData) => {
    setIsLoading(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    const finalData = {
      ...data,
      avatar: selectedAvatar,
      theme: selectedGender === 'female' ? 'pink' : 'blue'
    };

    console.log('Onboarding data:', finalData);
    setIsLoading(false);

    // Navigate to dashboard
    navigate('/dashboard');
  };

  const currentAvatars = selectedGender === 'female' ? girlAvatars : boyAvatars;
  const selectedAvatarData = currentAvatars.find(avatar => avatar.id === selectedAvatar);

  return (
    <div className="min-h-screen bg-dark-950 flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-950 to-black"></div>
      <div className="absolute inset-0 opacity-20" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      <div className="relative w-full max-w-2xl">
        <div className="card glass-effect animate-fade-in">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">تخصيص تجربتك</h1>
            <p className="text-gray-400">اختر اللغة والجنس والأفاتار المفضل لديك</p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Language Selection */}
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">اختر اللغة المفضلة</h3>
              <select
                {...register('language', {
                  required: 'يرجى اختيار اللغة'
                })}
                className="input-field"
              >
                {languageOptions.map((lang) => (
                  <option key={lang.value} value={lang.value} className="bg-dark-900 text-white">
                    {lang.label}
                  </option>
                ))}
              </select>
              {errors.language && (
                <p className="mt-1 text-sm text-red-400">{errors.language.message}</p>
              )}
            </div>

            {/* Gender Selection */}
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">اختر الجنس</h3>
              <div className="grid grid-cols-2 gap-4">
                <button
                  type="button"
                  onClick={() => handleGenderSelect('male')}
                  className={`p-6 rounded-xl border-2 transition-all duration-200 ${
                    selectedGender === 'male'
                      ? 'border-blue-500 bg-blue-500/10'
                      : 'border-dark-700 hover:border-blue-500/50'
                  }`}
                >
                  <div className="text-4xl mb-2">👨</div>
                  <div className="text-white font-medium">ذكر</div>
                </button>

                <button
                  type="button"
                  onClick={() => handleGenderSelect('female')}
                  className={`p-6 rounded-xl border-2 transition-all duration-200 ${
                    selectedGender === 'female'
                      ? 'border-pink-500 bg-pink-500/10'
                      : 'border-dark-700 hover:border-pink-500/50'
                  }`}
                >
                  <div className="text-4xl mb-2">👩</div>
                  <div className="text-white font-medium">أنثى</div>
                </button>
              </div>
            </div>

            {/* Avatar Selection */}
            {selectedGender && (
              <div>
                <h3 className="text-xl font-semibold text-white mb-4">اختر الأفاتار</h3>
                <p className="text-gray-400 mb-6">اختر أفاتار يمثلك في التطبيق</p>

                <div className="grid grid-cols-3 gap-4 mb-6">
                  {currentAvatars.map((avatar) => (
                    <button
                      key={avatar.id}
                      type="button"
                      onClick={() => handleAvatarSelect(avatar.id)}
                      className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                        selectedAvatar === avatar.id
                          ? selectedGender === 'female'
                            ? 'border-pink-500 bg-pink-500/10 avatar-girl'
                            : 'border-blue-500 bg-blue-500/10 avatar-boy'
                          : 'border-dark-700 hover:border-gray-500'
                      }`}
                    >
                      <div className="text-3xl mb-2">{avatar.emoji}</div>
                      <div className="text-white text-sm font-medium">{avatar.name}</div>
                    </button>
                  ))}
                </div>

                {selectedAvatarData && (
                  <div className={`p-6 rounded-xl border-2 ${
                    selectedGender === 'female'
                      ? 'border-pink-500 bg-pink-500/10'
                      : 'border-blue-500 bg-blue-500/10'
                  }`}>
                    <div className="text-center">
                      <div className="text-5xl mb-2">{selectedAvatarData.emoji}</div>
                      <div className="text-white font-semibold">{selectedAvatarData.name}</div>
                      <div className="text-gray-400 text-sm">الأفاتار المختار</div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading || !selectedGender}
              className="btn btn-primary w-full py-4 text-lg font-semibold"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-dark-950" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  جاري الإعداد...
                </div>
              ) : (
                'إكمال الإعداد'
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;
