import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

interface OnboardingFormData {
  language: string;
  gender: 'male' | 'female';
}

const languageOptions = [
  { value: 'ar', label: 'العربية' },
  { value: 'en', label: 'English' },
  { value: 'fr', label: 'Français' },
  { value: 'es', label: 'Español' },
  { value: 'de', label: 'Deutsch' },
  { value: 'it', label: 'Italiano' },
  { value: 'pt', label: 'Português' },
  { value: 'ru', label: 'Русский' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'zh', label: '中文' },
  { value: 'hi', label: 'हिन्दी' }
];

const boyAvatars = [
  { id: 'boy-1', name: 'أحمد', emoji: '👨‍💼' },
  { id: 'boy-2', name: 'محمد', emoji: '👨‍🎓' },
  { id: 'boy-3', name: 'علي', emoji: '👨‍💻' },
  { id: 'boy-4', name: 'عمر', emoji: '👨‍🔬' },
  { id: 'boy-5', name: 'يوسف', emoji: '👨‍🎨' },
  { id: 'boy-6', name: 'كريم', emoji: '👨‍🏫' }
];

const girlAvatars = [
  { id: 'girl-1', name: 'فاطمة', emoji: '👩‍💼' },
  { id: 'girl-2', name: 'عائشة', emoji: '👩‍🎓' },
  { id: 'girl-3', name: 'مريم', emoji: '👩‍💻' },
  { id: 'girl-4', name: 'زينب', emoji: '👩‍🔬' },
  { id: 'girl-5', name: 'سارة', emoji: '👩‍🎨' },
  { id: 'girl-6', name: 'نور', emoji: '👩‍🏫' }
];

export const OnboardingPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState<string>('');
  const [selectedGender, setSelectedGender] = useState<'male' | 'female' | ''>('');
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<OnboardingFormData>({
    defaultValues: {
      language: 'ar',
      gender: 'male'
    }
  });

  const watchedLanguage = watch('language');

  const handleGenderSelect = (gender: 'male' | 'female') => {
    setSelectedGender(gender);
    setValue('gender', gender);
    setSelectedAvatar(''); // Reset avatar when gender changes
  };

  const handleAvatarSelect = (avatarId: string) => {
    setSelectedAvatar(avatarId);
  };

  const onSubmit = async (data: OnboardingFormData) => {
    setIsLoading(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    const finalData = {
      ...data,
      avatar: selectedAvatar,
      theme: selectedGender === 'female' ? 'pink' : 'blue'
    };

    console.log('Onboarding data:', finalData);
    setIsLoading(false);

    // Navigate to dashboard
    navigate('/dashboard');
  };

  const currentAvatars = selectedGender === 'female' ? girlAvatars : boyAvatars;
  const selectedAvatarData = currentAvatars.find(avatar => avatar.id === selectedAvatar);

  return (
    <div className="onboarding-page">
      <div className="onboarding-container">
        <div className="onboarding-header">
          <div className="progress-indicator">
            <div className="progress-step progress-step--completed">
              <span className="progress-number">1</span>
              <svg className="progress-check" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="progress-line progress-line--completed"></div>
            <div className="progress-step progress-step--active">
              <span className="progress-number">2</span>
            </div>
          </div>
          
          <h1 className="onboarding-title">Personalize Your Experience</h1>
          <p className="onboarding-subtitle">Step 2 of 2: Preferences & Avatar</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="onboarding-form">
          <div className="form-section">
            <h3 className="section-title">Language Preference</h3>
            <Select
              {...register('language', {
                required: 'Please select a language'
              })}
              label="Preferred Language"
              placeholder="Select your preferred language"
              options={languageOptions}
              error={errors.language?.message}
              fullWidth
            />
          </div>

          <div className="form-section">
            <h3 className="section-title">Gender</h3>
            <RadioGroup
              name="gender"
              options={genderOptions}
              value={watchedGender}
              onChange={(value) => setValue('gender', value)}
              label="Gender (Optional)"
              direction="horizontal"
              error={errors.gender?.message}
            />
          </div>

          <div className="form-section">
            <h3 className="section-title">Choose Your Avatar</h3>
            <p className="section-description">
              Select an avatar to represent you, or skip this step for now.
            </p>
            
            <div className="avatar-grid">
              {avatarOptions.map((avatar) => (
                <button
                  key={avatar.id}
                  type="button"
                  className={`avatar-option ${selectedAvatar === avatar.id ? 'avatar-option--selected' : ''}`}
                  onClick={() => handleAvatarSelect(avatar.id)}
                  style={{ '--avatar-color': avatar.color } as React.CSSProperties}
                >
                  <div className="avatar-emoji">{avatar.emoji}</div>
                  <span className="avatar-name">{avatar.name}</span>
                </button>
              ))}
            </div>

            {selectedAvatarData && (
              <div className="avatar-preview">
                <h4 className="preview-title">Selected Avatar</h4>
                <div className="preview-avatar" style={{ '--avatar-color': selectedAvatarData.color } as React.CSSProperties}>
                  <div className="preview-emoji">{selectedAvatarData.emoji}</div>
                  <span className="preview-name">{selectedAvatarData.name}</span>
                </div>
              </div>
            )}

            <Button
              type="button"
              variant="ghost"
              onClick={handleSkipAvatar}
              className="skip-avatar-btn"
            >
              Skip for now
            </Button>
          </div>

          <div className="form-actions">
            <Button
              type="submit"
              variant="primary"
              size="lg"
              fullWidth
              isLoading={isLoading}
              disabled={!watchedLanguage}
            >
              Complete Setup
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default OnboardingPage;
