.radio-group {
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.radio-group-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-2);
}

.radio-group-options {
  display: flex;
  gap: var(--spacing-4);
}

.radio-group--vertical .radio-group-options {
  flex-direction: column;
  gap: var(--spacing-3);
}

.radio-group--horizontal .radio-group-options {
  flex-direction: row;
  flex-wrap: wrap;
  gap: var(--spacing-6);
}

.radio-option {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  cursor: pointer;
}

.radio-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.radio-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  min-width: 20px;
  min-height: 20px;
  background-color: var(--color-bg-primary);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  margin-top: 2px; /* Align with first line of text */
}

.radio-dot {
  width: 8px;
  height: 8px;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  opacity: 0;
  transform: scale(0);
  transition: all var(--transition-fast);
}

.radio-input:checked + .radio-indicator {
  border-color: var(--color-primary);
}

.radio-input:checked + .radio-indicator .radio-dot {
  opacity: 1;
  transform: scale(1);
}

.radio-input:focus + .radio-indicator {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.radio-input:disabled + .radio-indicator {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-300);
  cursor: not-allowed;
}

.radio-input:disabled:checked + .radio-indicator .radio-dot {
  background-color: var(--color-gray-400);
}

.radio-group--error .radio-indicator {
  border-color: var(--color-border-error);
}

.radio-group--error .radio-input:focus + .radio-indicator {
  outline-color: var(--color-border-error);
}

.radio-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
  cursor: pointer;
  user-select: none;
}

.radio-input:disabled ~ .radio-label {
  color: var(--color-text-muted);
  cursor: not-allowed;
}

.radio-group-error {
  font-size: var(--font-size-sm);
  color: var(--color-error);
  line-height: var(--line-height-tight);
}

.radio-group-helper {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  line-height: var(--line-height-tight);
}

/* Hover states */
.radio-option:hover .radio-input:not(:disabled) + .radio-indicator {
  border-color: var(--color-primary);
}

/* Touch-friendly sizing for mobile */
@media (max-width: 767px) {
  .radio-indicator {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
  }
  
  .radio-dot {
    width: 10px;
    height: 10px;
  }
  
  .radio-group--horizontal .radio-group-options {
    gap: var(--spacing-4);
  }
}

/* Responsive layout for horizontal groups */
@media (max-width: 480px) {
  .radio-group--horizontal .radio-group-options {
    flex-direction: column;
    gap: var(--spacing-3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .radio-indicator,
  .radio-dot {
    transition: none;
  }
}
