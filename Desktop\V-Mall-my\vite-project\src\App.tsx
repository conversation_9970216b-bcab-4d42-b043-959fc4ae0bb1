import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { LoginPage, SignUpPage, OnboardingPage } from './pages';
import './App.css';

function App() {
  return (
    <Router>
      <div className="app">
        <Routes>
          <Route path="/" element={<Navigate to="/login" replace />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignUpPage />} />
          <Route path="/onboarding" element={<OnboardingPage />} />
          <Route path="/dashboard" element={<DashboardPlaceholder />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

// Placeholder component for dashboard
const DashboardPlaceholder: React.FC = () => {
  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column',
      gap: '1rem',
      padding: '2rem',
      textAlign: 'center'
    }}>
      <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--color-text-primary)' }}>
        🎉 Welcome to Your Dashboard!
      </h1>
      <p style={{ fontSize: '1.125rem', color: 'var(--color-text-secondary)' }}>
        You have successfully completed the onboarding process.
      </p>
      <p style={{ fontSize: '0.875rem', color: 'var(--color-text-muted)' }}>
        This is where your main application would be.
      </p>
    </div>
  );
};

export default App;
